import React from 'react';
import createMatchMedia from 'tests/create-match-media';
import { render, screen, within } from 'tests/test-utils';
import { HeatmapLevelItem } from './HeatmapLevelItem';

describe('<HeatmapLevelItem />', () => {
  const heatmapLevelItemProps = {
    title: 'Not useful',
    scoreRange: 'Score range: 0-19%',
    description: 'Issues are severely lacking in essential information or have been inactive for too long.',
    className: 'bg-red-500',
  };

  it('renders correct title', () => {
    render(<HeatmapLevelItem {...heatmapLevelItemProps} />);

    expect(screen.getByText('Not useful')).toBeInTheDocument();
  });

  describe('when user hovers over the item', () => {
    describe('when screen is large', () => {
      beforeEach(() => {
        window.matchMedia = createMatchMedia(1024);
      });
      it('shows the popover with correct content', async () => {
        const { user } = render(<HeatmapLevelItem {...heatmapLevelItemProps} />);

        await user.hover(screen.getByLabelText('Not useful'));
        const popover = screen.getByRole('dialog');

        expect(within(popover).getByText(heatmapLevelItemProps.title)).toBeInTheDocument();
        expect(await screen.findByText(heatmapLevelItemProps.scoreRange)).toBeInTheDocument();
        expect(await screen.findByText(heatmapLevelItemProps.description)).toBeInTheDocument();
      });
    });

    describe('when screen is small', () => {
      beforeEach(() => {
        window.matchMedia = createMatchMedia(640);
      });

      it('does not show the popover on pointer over', async () => {
        const { user } = render(<HeatmapLevelItem {...heatmapLevelItemProps} />);

        await user.hover(screen.getByLabelText('Not useful'));

        expect(screen.queryByText(heatmapLevelItemProps.scoreRange)).not.toBeInTheDocument();
        expect(screen.queryByText(heatmapLevelItemProps.description)).not.toBeInTheDocument();
      });

      it('shows the popover on click', async () => {
        const { user } = render(<HeatmapLevelItem {...heatmapLevelItemProps} />);

        await user.click(screen.getByLabelText('Not useful'));
        const popover = screen.getByRole('dialog');

        expect(within(popover).getByText(heatmapLevelItemProps.title)).toBeInTheDocument();
        expect(await screen.findByText(heatmapLevelItemProps.scoreRange)).toBeInTheDocument();
        expect(await screen.findByText(heatmapLevelItemProps.description)).toBeInTheDocument();
      });
    });
  });
});
