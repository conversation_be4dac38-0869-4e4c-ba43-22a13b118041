import { useMessageGetter } from '@messageformat/react';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useDataHealth } from '../../data-health-heatmap/DataHealthContext';
import { ScoringInfo } from '../ScoringInfo/ScoringInfo';
import { HeatmapLevelItem } from './HeatmapLevelItem';
import { getHeatmapColorClasses, HEATMAP_LEVELS, HEATMAP_THEME } from './heatmap-config';

export const HeatmapLegend = () => {
  const messages = useMessageGetter('dataBook.page.heatmapDashboard');
  const { recordType } = useDataHealth();
  const showScoringIndicator = recordType === 'issues';
  return (
    <div className="overflow-x-auto">
      <div
        className={cn('flex flex-row px-4 py-2 min-w-max items-center justify-end', {
          'justify-between': showScoringIndicator,
        })}
      >
        <ScoringInfo />
        <div className="flex">
          {HEATMAP_LEVELS.slice(1).map((level) => {
            const colorClasses = getHeatmapColorClasses(HEATMAP_THEME, level, 'badge');
            return (
              <HeatmapLevelItem
                key={level}
                className={cn('mx-4 last:mr-0', colorClasses)}
                title={messages(`healthLevels.${level}.label`)}
                scoreRange={messages(`healthLevels.${level}.issues.scoreRange`)}
                description={messages(`healthLevels.${level}.issues.description`)}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
};
